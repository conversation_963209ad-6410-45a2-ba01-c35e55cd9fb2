# Installation Guide for Kiwi Primer Design Pipeline

## System Dependencies

### 1. Install BCFtools and SAMtools
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install bcftools samtools

# CentOS/RHEL
sudo yum install bcftools samtools

# Or compile from source
wget https://github.com/samtools/bcftools/releases/download/1.17/bcftools-1.17.tar.bz2
tar -xjf bcftools-1.17.tar.bz2
cd bcftools-1.17
make
sudo make install
```

### 2. Install Primer3
```bash
# Download and compile Primer3
wget https://github.com/primer3-org/primer3/archive/v2.6.1.tar.gz
tar -xzf v2.6.1.tar.gz
cd primer3-2.6.1/src
make
sudo cp primer3_core /usr/local/bin/
```

### 3. Install BLAST+
```bash
# Ubuntu/Debian
sudo apt-get install ncbi-blast+

# CentOS/RHEL
sudo yum install ncbi-blast+

# Or download from NCBI
wget https://ftp.ncbi.nlm.nih.gov/blast/executables/blast+/LATEST/ncbi-blast-2.14.0+-x64-linux.tar.gz
tar -xzf ncbi-blast-2.14.0+-x64-linux.tar.gz
sudo cp ncbi-blast-2.14.0+/bin/* /usr/local/bin/
```

## Python Dependencies

### Install required Python packages
```bash
pip3 install pandas numpy biopython pyyaml matplotlib seaborn

# Or using conda
conda install pandas numpy biopython pyyaml matplotlib seaborn
```

## Verify Installation
After installing dependencies, run:
```bash
python3 simple_system_check.py
```

## Quick Start
1. Install all dependencies above
2. Run system check: `python3 simple_system_check.py`
3. Create config: `python3 run_variety_primer_design.py --create-config`
4. Run pipeline: `python3 run_variety_primer_design.py --config primer_design_config.yaml`
