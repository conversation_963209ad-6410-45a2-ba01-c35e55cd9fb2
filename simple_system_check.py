#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple System Check for Kiwi Primer Design Pipeline
===================================================

Basic system check that doesn't require additional Python packages.

Author: Augment Agent
Date: 2025-07-12
"""

import subprocess
import sys
import os

def check_command(command, description):
    """Check if a command is available"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ {description}: Available")
            return True
        else:
            print(f"✗ {description}: Not available or version check failed")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        try:
            # Try alternative version check
            result = subprocess.run([command, '-h'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ {description}: Available")
                return True
            else:
                print(f"✗ {description}: Not available")
                return False
        except:
            print(f"✗ {description}: Not available")
            return False

def check_python_packages():
    """Check required Python packages"""
    required_packages = [
        'pandas', 'numpy', 'Bio', 'yaml', 'matplotlib', 'seaborn'
    ]
    
    missing_packages = []
    available_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ Python package {package}: Available")
            available_packages.append(package)
        except ImportError:
            print(f"✗ Python package {package}: Missing")
            missing_packages.append(package)
    
    return missing_packages, available_packages

def check_input_files():
    """Check if required input files exist"""
    required_files = [
        'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz',
        'kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv'
    ]
    
    optional_files = [
        'kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna'
    ]
    
    all_good = True
    found_files = []
    missing_files = []
    
    print("\nChecking required input files:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}: Found")
            found_files.append(file_path)
        else:
            print(f"✗ {file_path}: Missing")
            missing_files.append(file_path)
            all_good = False
    
    print("\nChecking optional input files:")
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}: Found")
            found_files.append(file_path)
        else:
            print(f"? {file_path}: Not found (may need to be created)")
    
    return all_good, found_files, missing_files

def create_installation_guide():
    """Create installation guide"""
    guide_content = """# Installation Guide for Kiwi Primer Design Pipeline

## System Dependencies

### 1. Install BCFtools and SAMtools
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install bcftools samtools

# CentOS/RHEL
sudo yum install bcftools samtools

# Or compile from source
wget https://github.com/samtools/bcftools/releases/download/1.17/bcftools-1.17.tar.bz2
tar -xjf bcftools-1.17.tar.bz2
cd bcftools-1.17
make
sudo make install
```

### 2. Install Primer3
```bash
# Download and compile Primer3
wget https://github.com/primer3-org/primer3/archive/v2.6.1.tar.gz
tar -xzf v2.6.1.tar.gz
cd primer3-2.6.1/src
make
sudo cp primer3_core /usr/local/bin/
```

### 3. Install BLAST+
```bash
# Ubuntu/Debian
sudo apt-get install ncbi-blast+

# CentOS/RHEL
sudo yum install ncbi-blast+

# Or download from NCBI
wget https://ftp.ncbi.nlm.nih.gov/blast/executables/blast+/LATEST/ncbi-blast-2.14.0+-x64-linux.tar.gz
tar -xzf ncbi-blast-2.14.0+-x64-linux.tar.gz
sudo cp ncbi-blast-2.14.0+/bin/* /usr/local/bin/
```

## Python Dependencies

### Install required Python packages
```bash
pip3 install pandas numpy biopython pyyaml matplotlib seaborn

# Or using conda
conda install pandas numpy biopython pyyaml matplotlib seaborn
```

## Verify Installation
After installing dependencies, run:
```bash
python3 simple_system_check.py
```

## Quick Start
1. Install all dependencies above
2. Run system check: `python3 simple_system_check.py`
3. Create config: `python3 run_variety_primer_design.py --create-config`
4. Run pipeline: `python3 run_variety_primer_design.py --config primer_design_config.yaml`
"""
    
    with open('INSTALLATION_GUIDE.md', 'w') as f:
        f.write(guide_content)
    
    print("✓ Created installation guide: INSTALLATION_GUIDE.md")

def main():
    """Main test function"""
    print("=" * 60)
    print("KIWI PRIMER DESIGN PIPELINE - BASIC SYSTEM CHECK")
    print("=" * 60)
    
    # Check system commands
    print("\n1. Checking system dependencies:")
    commands_ok = True
    commands_ok &= check_command('bcftools', 'BCFtools')
    commands_ok &= check_command('samtools', 'SAMtools')
    commands_ok &= check_command('primer3_core', 'Primer3')
    commands_ok &= check_command('makeblastdb', 'BLAST makeblastdb')
    commands_ok &= check_command('blastn', 'BLAST blastn')
    
    # Check Python packages
    print("\n2. Checking Python packages:")
    missing_packages, available_packages = check_python_packages()
    
    # Check input files
    print("\n3. Checking input files:")
    files_ok, found_files, missing_files = check_input_files()
    
    # Create installation guide
    print("\n4. Creating installation guide:")
    create_installation_guide()
    
    # Summary
    print("\n" + "=" * 60)
    print("SYSTEM CHECK SUMMARY")
    print("=" * 60)
    
    if commands_ok:
        print("✓ All system commands available")
    else:
        print("✗ Some system commands missing")
        print("  See INSTALLATION_GUIDE.md for installation instructions")
    
    if not missing_packages:
        print("✓ All Python packages available")
    else:
        print(f"✗ Missing Python packages: {', '.join(missing_packages)}")
        print("  Install with: pip3 install " + " ".join(missing_packages))
    
    if files_ok:
        print("✓ Required input files found")
    else:
        print("✗ Some required input files missing:")
        for file in missing_files:
            print(f"    - {file}")
    
    # Overall assessment
    all_ok = commands_ok and not missing_packages and files_ok
    
    print("\n" + "-" * 60)
    if all_ok:
        print("🎉 SYSTEM CHECK PASSED - Ready to run primer design pipeline!")
        print("\nNext steps:")
        print("1. python3 run_variety_primer_design.py --create-config")
        print("2. python3 run_variety_primer_design.py --config primer_design_config.yaml")
    else:
        print("⚠️  SYSTEM CHECK INCOMPLETE - Please address the issues above")
        print("\nRecommended actions:")
        if not commands_ok:
            print("- Install missing system dependencies (see INSTALLATION_GUIDE.md)")
        if missing_packages:
            print(f"- Install Python packages: pip3 install {' '.join(missing_packages)}")
        if not files_ok:
            print("- Ensure your pangenome analysis results are in the expected locations")
        print("- Review INSTALLATION_GUIDE.md for detailed instructions")
    
    return all_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {e}")
        sys.exit(1)
