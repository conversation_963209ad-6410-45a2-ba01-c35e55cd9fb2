#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Primer Design Module for Kiwi Variety-Specific Markers
======================================================

This module handles the actual primer design using Primer3 and validation
of primer specificity for variety identification.

Author: Augment Agent
Date: 2025-07-12
"""

import pandas as pd
import numpy as np
import subprocess
import os
import tempfile
import logging
from pathlib import Path
from Bio.Seq import Seq
from Bio import SeqIO
import json
import re

logger = logging.getLogger(__name__)

class PrimerDesigner:
    """Class for designing and validating primers"""
    
    def __init__(self, reference_genome, output_dir):
        """
        Initialize primer designer
        
        Args:
            reference_genome (str): Path to reference genome FASTA
            output_dir (str): Output directory
        """
        self.reference_genome = reference_genome
        self.output_dir = Path(output_dir)
        self.primer_results = []
        
        # Primer design parameters
        self.primer_params = {
            'PRIMER_OPT_SIZE': 20,
            'PRIMER_MIN_SIZE': 18,
            'PRIMER_MAX_SIZE': 25,
            'PRIMER_OPT_TM': 60.0,
            'PRIMER_MIN_TM': 57.0,
            'PRIMER_MAX_TM': 63.0,
            'PRIMER_MIN_GC': 40.0,
            'PRIMER_MAX_GC': 60.0,
            'PRIMER_PRODUCT_SIZE_RANGE': '100-300',  # Optimal for variety identification
            'PRIMER_NUM_RETURN': 5
        }
        
        # Ensure reference genome is indexed
        self.ensure_genome_indexed()
    
    def ensure_genome_indexed(self):
        """Ensure reference genome is indexed with samtools"""
        fai_file = f"{self.reference_genome}.fai"
        if not os.path.exists(fai_file):
            logger.info("Creating reference genome index...")
            try:
                subprocess.run(['samtools', 'faidx', self.reference_genome], check=True)
                logger.info("Reference genome indexed successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to index reference genome: {e}")
                raise
    
    def extract_flanking_sequence(self, chrom, pos, flank_size=500):
        """
        Extract flanking sequence around a variant position
        
        Args:
            chrom (str): Chromosome/contig name
            pos (int): Position of variant
            flank_size (int): Size of flanking region on each side
            
        Returns:
            dict: Sequence information including variant position
        """
        start = max(1, pos - flank_size)
        end = pos + flank_size
        
        cmd = ['samtools', 'faidx', self.reference_genome, f"{chrom}:{start}-{end}"]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Parse FASTA output
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                sequence = ''.join(lines[1:]).upper()
                
                # Calculate variant position in extracted sequence
                variant_pos_in_seq = pos - start
                
                return {
                    'sequence': sequence,
                    'variant_position': variant_pos_in_seq,
                    'flanking_start': start,
                    'flanking_end': end,
                    'chrom': chrom,
                    'original_pos': pos
                }
        
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to extract sequence for {chrom}:{pos}: {e}")
        
        return None
    
    def design_primers_primer3(self, sequence_info, variant_info, output_prefix):
        """
        Design primers using Primer3
        
        Args:
            sequence_info (dict): Sequence information from extract_flanking_sequence
            variant_info (dict): Variant information
            output_prefix (str): Prefix for output files
            
        Returns:
            list: List of primer pairs
        """
        sequence = sequence_info['sequence']
        variant_pos = sequence_info['variant_position']
        
        # Define target region around the variant (±50bp)
        target_start = max(0, variant_pos - 50)
        target_length = min(100, len(sequence) - target_start)
        
        # Create Primer3 input
        primer3_input = f"""SEQUENCE_ID={output_prefix}
SEQUENCE_TEMPLATE={sequence}
SEQUENCE_TARGET={target_start},{target_length}
PRIMER_TASK=generic
PRIMER_PICK_LEFT_PRIMER=1
PRIMER_PICK_INTERNAL_OLIGO=0
PRIMER_PICK_RIGHT_PRIMER=1
PRIMER_OPT_SIZE={self.primer_params['PRIMER_OPT_SIZE']}
PRIMER_MIN_SIZE={self.primer_params['PRIMER_MIN_SIZE']}
PRIMER_MAX_SIZE={self.primer_params['PRIMER_MAX_SIZE']}
PRIMER_OPT_TM={self.primer_params['PRIMER_OPT_TM']}
PRIMER_MIN_TM={self.primer_params['PRIMER_MIN_TM']}
PRIMER_MAX_TM={self.primer_params['PRIMER_MAX_TM']}
PRIMER_MIN_GC={self.primer_params['PRIMER_MIN_GC']}
PRIMER_MAX_GC={self.primer_params['PRIMER_MAX_GC']}
PRIMER_PRODUCT_SIZE_RANGE={self.primer_params['PRIMER_PRODUCT_SIZE_RANGE']}
PRIMER_NUM_RETURN={self.primer_params['PRIMER_NUM_RETURN']}
PRIMER_EXPLAIN_FLAG=1
=
"""
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(primer3_input)
            input_file = f.name
        
        # Run Primer3
        output_file = f"{output_prefix}_primers.txt"
        
        try:
            with open(output_file, 'w') as f:
                subprocess.run(['primer3_core'], stdin=open(input_file), 
                             stdout=f, check=True)
            
            # Parse results
            primers = self.parse_primer3_output(output_file)
            
            # Add metadata to each primer pair
            for primer in primers:
                primer.update({
                    'variant_info': variant_info,
                    'sequence_info': sequence_info,
                    'design_params': self.primer_params.copy()
                })
            
            # Clean up
            os.unlink(input_file)
            
            return primers
        
        except subprocess.CalledProcessError as e:
            logger.warning(f"Primer3 failed for {output_prefix}: {e}")
            os.unlink(input_file)
            return []
    
    def parse_primer3_output(self, output_file):
        """
        Parse Primer3 output file
        
        Args:
            output_file (str): Path to Primer3 output file
            
        Returns:
            list: List of primer pair dictionaries
        """
        primers = []
        
        try:
            with open(output_file, 'r') as f:
                content = f.read()
            
            # Parse key-value pairs
            primer_data = {}
            for line in content.split('\n'):
                if '=' in line:
                    key, value = line.split('=', 1)
                    primer_data[key] = value
            
            # Extract primer pairs
            for i in range(self.primer_params['PRIMER_NUM_RETURN']):
                left_seq_key = f"PRIMER_LEFT_{i}_SEQUENCE"
                right_seq_key = f"PRIMER_RIGHT_{i}_SEQUENCE"
                
                if left_seq_key in primer_data and right_seq_key in primer_data:
                    primer_pair = {
                        'pair_id': i,
                        'left_primer': primer_data[left_seq_key],
                        'right_primer': primer_data[right_seq_key],
                        'left_tm': float(primer_data.get(f"PRIMER_LEFT_{i}_TM", 0)),
                        'right_tm': float(primer_data.get(f"PRIMER_RIGHT_{i}_TM", 0)),
                        'left_gc': float(primer_data.get(f"PRIMER_LEFT_{i}_GC_PERCENT", 0)),
                        'right_gc': float(primer_data.get(f"PRIMER_RIGHT_{i}_GC_PERCENT", 0)),
                        'product_size': int(primer_data.get(f"PRIMER_PAIR_{i}_PRODUCT_SIZE", 0)),
                        'pair_penalty': float(primer_data.get(f"PRIMER_PAIR_{i}_PENALTY", 999))
                    }
                    primers.append(primer_pair)
            
            return primers
        
        except Exception as e:
            logger.warning(f"Failed to parse Primer3 output {output_file}: {e}")
            return []
    
    def validate_primer_specificity(self, primer_sequence, max_hits=10):
        """
        Validate primer specificity using BLAST against reference genome
        
        Args:
            primer_sequence (str): Primer sequence
            max_hits (int): Maximum number of BLAST hits to consider
            
        Returns:
            dict: Specificity validation results
        """
        # Create temporary FASTA file for primer
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as f:
            f.write(f">primer\n{primer_sequence}\n")
            primer_file = f.name
        
        # Create BLAST database if it doesn't exist
        blast_db = f"{self.reference_genome}.blast"
        if not os.path.exists(f"{blast_db}.nhr"):
            logger.info("Creating BLAST database...")
            try:
                subprocess.run([
                    'makeblastdb', '-in', self.reference_genome,
                    '-dbtype', 'nucl', '-out', blast_db
                ], check=True, capture_output=True)
            except subprocess.CalledProcessError as e:
                logger.warning(f"Failed to create BLAST database: {e}")
                os.unlink(primer_file)
                return {'specificity_score': 0.5, 'blast_hits': 0, 'error': str(e)}
        
        # Run BLAST
        try:
            result = subprocess.run([
                'blastn', '-query', primer_file, '-db', blast_db,
                '-outfmt', '6 qseqid sseqid pident length mismatch gapopen qstart qend sstart send evalue bitscore',
                '-max_target_seqs', str(max_hits), '-word_size', '7'
            ], capture_output=True, text=True, check=True)
            
            # Parse BLAST results
            blast_lines = result.stdout.strip().split('\n')
            hits = []
            
            for line in blast_lines:
                if line:
                    parts = line.split('\t')
                    if len(parts) >= 12:
                        hit = {
                            'subject': parts[1],
                            'identity': float(parts[2]),
                            'length': int(parts[3]),
                            'mismatches': int(parts[4]),
                            'evalue': float(parts[10]),
                            'bitscore': float(parts[11])
                        }
                        hits.append(hit)
            
            # Calculate specificity score
            specificity_score = self.calculate_specificity_score(hits, len(primer_sequence))
            
            # Clean up
            os.unlink(primer_file)
            
            return {
                'specificity_score': specificity_score,
                'blast_hits': len(hits),
                'top_hits': hits[:5],  # Keep top 5 hits for analysis
                'perfect_matches': len([h for h in hits if h['identity'] == 100.0])
            }
        
        except subprocess.CalledProcessError as e:
            logger.warning(f"BLAST failed for primer validation: {e}")
            os.unlink(primer_file)
            return {'specificity_score': 0.5, 'blast_hits': 0, 'error': str(e)}
    
    def calculate_specificity_score(self, blast_hits, primer_length):
        """
        Calculate primer specificity score based on BLAST hits
        
        Args:
            blast_hits (list): List of BLAST hit dictionaries
            primer_length (int): Length of primer sequence
            
        Returns:
            float: Specificity score (0-1, higher is more specific)
        """
        if not blast_hits:
            return 0.9  # High specificity if no hits
        
        # Count high-identity hits
        high_identity_hits = [h for h in blast_hits if h['identity'] >= 90.0]
        perfect_matches = [h for h in blast_hits if h['identity'] == 100.0]
        
        # Base score starts high and decreases with more hits
        base_score = 1.0
        
        # Penalty for multiple perfect matches (should ideally be 1)
        if len(perfect_matches) > 1:
            base_score -= 0.3 * (len(perfect_matches) - 1)
        
        # Penalty for high-identity hits
        if len(high_identity_hits) > 1:
            base_score -= 0.1 * (len(high_identity_hits) - 1)
        
        # Penalty for total number of hits
        base_score -= 0.05 * len(blast_hits)
        
        return max(0.0, min(1.0, base_score))
    
    def check_primer_dimers(self, left_primer, right_primer):
        """
        Check for potential primer-dimer formation
        
        Args:
            left_primer (str): Left primer sequence
            right_primer (str): Right primer sequence
            
        Returns:
            dict: Primer-dimer analysis results
        """
        # Simple primer-dimer check based on complementarity
        # This is a simplified implementation - more sophisticated tools like
        # Primer3's built-in checks or specialized software could be used
        
        left_rev_comp = str(Seq(left_primer).reverse_complement())
        
        # Check for complementarity between primers
        max_complementarity = 0
        max_3prime_complementarity = 0
        
        # Check all possible alignments
        for i in range(len(left_primer)):
            for j in range(len(right_primer)):
                # Check complementarity
                comp_score = 0
                for k in range(min(len(left_primer) - i, len(right_primer) - j)):
                    if self.is_complementary(left_primer[i + k], right_primer[j + k]):
                        comp_score += 1
                    else:
                        break
                
                max_complementarity = max(max_complementarity, comp_score)
                
                # Check 3' end complementarity (more critical)
                if i >= len(left_primer) - 5:  # Last 5 bases
                    max_3prime_complementarity = max(max_3prime_complementarity, comp_score)
        
        # Risk assessment
        dimer_risk = "LOW"
        if max_3prime_complementarity >= 4:
            dimer_risk = "HIGH"
        elif max_3prime_complementarity >= 3 or max_complementarity >= 8:
            dimer_risk = "MEDIUM"
        
        return {
            'max_complementarity': max_complementarity,
            'max_3prime_complementarity': max_3prime_complementarity,
            'dimer_risk': dimer_risk
        }
    
    def is_complementary(self, base1, base2):
        """Check if two bases are complementary"""
        complements = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G'}
        return complements.get(base1.upper()) == base2.upper()
    
    def design_primers_for_variety(self, variety_name, variants_file, max_primers_per_variety=10):
        """
        Design primers for a specific variety based on its specific variants
        
        Args:
            variety_name (str): Name of the variety
            variants_file (str): Path to variety-specific variants file
            max_primers_per_variety (int): Maximum number of primer pairs per variety
            
        Returns:
            list: List of designed primer pairs with validation results
        """
        logger.info(f"Designing primers for variety: {variety_name}")
        
        try:
            # Load variety-specific variants
            variants_df = pd.read_csv(variants_file, sep='\t')
            
            # Sort by specificity score and take top variants
            variants_df = variants_df.sort_values('SPECIFICITY_SCORE', ascending=False)
            top_variants = variants_df.head(max_primers_per_variety * 2)  # Get extra in case some fail
            
            variety_primers = []
            
            for idx, variant in top_variants.iterrows():
                if len(variety_primers) >= max_primers_per_variety:
                    break
                
                logger.info(f"  Processing variant {variant['CHROM']}:{variant['POS']}")
                
                # Extract flanking sequence
                sequence_info = self.extract_flanking_sequence(
                    variant['CHROM'], variant['POS'], flank_size=500
                )
                
                if not sequence_info:
                    continue
                
                # Design primers
                output_prefix = self.output_dir / "primer_sequences" / f"{variety_name}_var_{idx}"
                primers = self.design_primers_primer3(
                    sequence_info, variant.to_dict(), str(output_prefix)
                )
                
                # Validate each primer pair
                for primer in primers:
                    if len(variety_primers) >= max_primers_per_variety:
                        break
                    
                    # Validate left primer
                    left_validation = self.validate_primer_specificity(primer['left_primer'])
                    
                    # Validate right primer
                    right_validation = self.validate_primer_specificity(primer['right_primer'])
                    
                    # Check primer dimers
                    dimer_check = self.check_primer_dimers(
                        primer['left_primer'], primer['right_primer']
                    )
                    
                    # Add validation results
                    primer.update({
                        'variety': variety_name,
                        'left_validation': left_validation,
                        'right_validation': right_validation,
                        'dimer_check': dimer_check,
                        'overall_quality': self.calculate_overall_quality(
                            primer, left_validation, right_validation, dimer_check
                        )
                    })
                    
                    variety_primers.append(primer)
            
            logger.info(f"Designed {len(variety_primers)} primer pairs for {variety_name}")
            return variety_primers
        
        except Exception as e:
            logger.error(f"Failed to design primers for {variety_name}: {e}")
            return []
    
    def calculate_overall_quality(self, primer, left_val, right_val, dimer_check):
        """Calculate overall quality score for a primer pair"""
        # Base score from Primer3 penalty (lower is better)
        base_score = max(0, 1 - primer['pair_penalty'] / 10)
        
        # Specificity component
        specificity_score = (left_val['specificity_score'] + right_val['specificity_score']) / 2
        
        # Dimer penalty
        dimer_penalty = 0
        if dimer_check['dimer_risk'] == 'HIGH':
            dimer_penalty = 0.3
        elif dimer_check['dimer_risk'] == 'MEDIUM':
            dimer_penalty = 0.1
        
        # Tm balance (prefer similar Tm values)
        tm_diff = abs(primer['left_tm'] - primer['right_tm'])
        tm_penalty = min(0.2, tm_diff / 20)  # Penalty increases with Tm difference
        
        overall_score = (base_score * 0.3 + specificity_score * 0.5 - 
                        dimer_penalty - tm_penalty)
        
        return max(0, min(1, overall_score))
