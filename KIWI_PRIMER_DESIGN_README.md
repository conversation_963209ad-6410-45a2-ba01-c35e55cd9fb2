# Kiwi Variety-Specific Primer Design Pipeline

A comprehensive computational pipeline for designing variety-specific molecular markers (primers) for genetic identification and differentiation of 110 kiwi varieties based on pangenome variant data.

## Overview

This pipeline leverages your completed pangenome analysis to design PCR primers that can uniquely identify and distinguish each kiwi variety. The approach moves beyond population-level analysis to focus on individual variety discrimination using high-quality genomic variants.

## Key Features

- **Individual Variety Focus**: Designs primers specific to each of the 110 varieties, not just population groups
- **Quality-Based Filtering**: Implements comprehensive filtering for variant quality, allele frequency, and missing data
- **Primer Optimization**: Uses Primer3 with optimized parameters for variety identification (100-300bp amplicons)
- **Specificity Validation**: Includes BLAST-based validation and primer-dimer prediction
- **Multiplexing Assessment**: Evaluates primer compatibility for potential multiplexed PCR
- **Comprehensive Database**: Creates structured primer database with quality metrics and metadata

## Pipeline Components

### 1. Core Analysis Scripts

- `kiwi_variety_specific_primer_design.py`: Main variant analysis and variety-specific variant identification
- `primer_design_module.py`: Primer design using Primer3 and validation tools
- `run_variety_primer_design.py`: Complete workflow orchestration
- `primer_validation_tools.py`: Advanced validation and quality assessment tools

### 2. Input Requirements

- **VCF File**: `kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz`
- **Reference Genome**: `kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna`
- **Population Data** (optional): `kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv`

### 3. Software Dependencies

```bash
# Required tools (ensure these are in your PATH)
- bcftools (for VCF processing)
- samtools (for sequence extraction)
- primer3_core (for primer design)
- makeblastdb and blastn (for specificity validation)

# Python packages
- pandas
- numpy
- biopython
- pyyaml
- matplotlib
- seaborn
```

## Quick Start

### 1. Create Configuration File

```bash
python run_variety_primer_design.py --create-config
```

This creates `primer_design_config.yaml` with default settings. Edit the file paths as needed:

```yaml
vcf_file: 'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz'
reference_genome: 'kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna'
population_file: 'kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv'
output_directory: 'kiwi_variety_primers_output'
min_allele_freq: 0.05
max_missing: 0.1
max_primers_per_variety: 10
```

### 2. Run Complete Pipeline

```bash
python run_variety_primer_design.py --config primer_design_config.yaml
```

### 3. Validate and Assess Results

```bash
python primer_validation_tools.py \
    --database kiwi_variety_primers_output/kiwi_variety_primers_database.csv \
    --reference kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna \
    --output validation_results
```

## Output Structure

```
kiwi_variety_primers_output/
├── variety_specific_variants/          # Variety-specific variants for each sample
│   ├── AcD2301_PE400_specific_variants.txt
│   ├── AcD2302_PE400_specific_variants.txt
│   └── ...
├── primer_sequences/                   # Individual primer design files
├── variety_results/                    # Results organized by variety
│   ├── AcD2301_PE400/
│   │   ├── AcD2301_PE400_primers.csv
│   │   └── AcD2301_PE400_primers_detailed.json
│   └── ...
├── kiwi_variety_primers_database.csv   # Master primer database
├── variety_primer_summary.csv          # Summary statistics by variety
├── primer_design_report.txt            # Comprehensive analysis report
└── logs/                              # Analysis logs
```

## Key Output Files

### 1. Master Primer Database (`kiwi_variety_primers_database.csv`)

Contains all designed primers with the following columns:
- `Variety`: Kiwi variety name
- `Primer_ID`: Unique primer identifier
- `Chromosome`, `Position`: Genomic location of target variant
- `Left_Primer`, `Right_Primer`: Primer sequences
- `Left_Tm`, `Right_Tm`: Melting temperatures
- `Product_Size`: Expected PCR product size
- `Overall_Quality`: Quality score (0-1, higher is better)
- `Recommended`: Yes/No recommendation based on quality threshold

### 2. Variety Summary (`variety_primer_summary.csv`)

Summary statistics for each variety:
- `total_primers`: Total number of primer pairs designed
- `high_quality_primers`: Number of primers with quality ≥ 0.7
- `avg_quality`: Average quality score for the variety

## Primer Selection Guidelines

### Quality Thresholds
- **High Quality**: Overall_Quality ≥ 0.8 (recommended for critical applications)
- **Good Quality**: Overall_Quality ≥ 0.7 (suitable for most applications)
- **Acceptable**: Overall_Quality ≥ 0.6 (may require additional validation)

### Multiplexing Considerations
- Select primers with similar Tm values (within ±5°C)
- Ensure product sizes are distinguishable (≥50bp difference)
- Check for primer-dimer formation potential
- Validate experimentally before large-scale use

### Experimental Validation Recommendations
1. **Initial Testing**: Test top 3 primer pairs per variety
2. **Specificity Check**: Validate with DNA from multiple varieties
3. **Sensitivity Assessment**: Determine minimum DNA concentration required
4. **Reproducibility**: Test across different PCR conditions and equipment

## Advanced Usage

### Custom Variant Filtering

Modify filtering parameters in the configuration file:

```yaml
min_allele_freq: 0.02      # Lower threshold for rare variants
max_missing: 0.05          # Stricter missing data filter
max_primers_per_variety: 15 # More primer options per variety
```

### Population-Aware Analysis

If you want to incorporate population structure into primer design:

1. Ensure population file is properly formatted
2. The pipeline will use population information to enhance specificity scoring
3. Results will include population context in variant selection

### Batch Processing

For processing subsets of varieties:

```python
# Modify the workflow to process specific varieties
varieties_of_interest = ['AcD2301_PE400', 'AcD2302_PE400', 'AcD2303_PE400']
# Add filtering logic in the workflow script
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all required software is installed and in PATH
2. **Memory Issues**: Large VCF files may require substantial RAM (recommend 16GB+)
3. **Primer3 Failures**: Check that primer3_core is properly installed
4. **BLAST Database**: Ensure sufficient disk space for BLAST database creation

### Performance Optimization

- Use SSD storage for better I/O performance
- Consider parallel processing for large datasets
- Monitor memory usage during variant analysis

## Citation and Acknowledgments

This pipeline was developed as part of a comprehensive kiwi pangenome analysis project. If you use this pipeline in your research, please cite:

- The pangenome analysis methodology
- Primer3 software
- BCFtools and SAMtools
- Any relevant publications from your pangenome study

## Support and Contact

For questions, issues, or suggestions regarding this primer design pipeline, please:

1. Check the troubleshooting section above
2. Review the log files in the output directory
3. Ensure all dependencies are properly installed
4. Contact the development team with specific error messages and system details

---

**Note**: This pipeline is designed specifically for the kiwi pangenome dataset structure. Adaptation for other species or data formats may require modifications to the variant analysis and primer design parameters.
