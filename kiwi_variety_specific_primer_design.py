#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Kiwi Variety-Specific Primer Design Pipeline
============================================

This script designs variety-specific molecular markers (primers) for genetic identification
and differentiation of 110 kiwi varieties based on pangenome variant data.

Author: Augment Agent
Date: 2025-07-12
"""

import pandas as pd
import numpy as np
import subprocess
import os
import sys
import tempfile
import argparse
from pathlib import Path
from collections import defaultdict
import logging
from Bio import SeqIO
from Bio.Seq import Seq
import vcf
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('primer_design.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KiwiPrimerDesigner:
    """Main class for designing variety-specific primers for kiwi varieties"""
    
    def __init__(self, vcf_file, reference_genome, output_dir, population_file=None):
        """
        Initialize the primer designer
        
        Args:
            vcf_file (str): Path to merged VCF file
            reference_genome (str): Path to reference genome FASTA
            output_dir (str): Output directory for results
            population_file (str): Optional population assignment file
        """
        self.vcf_file = vcf_file
        self.reference_genome = reference_genome
        self.output_dir = Path(output_dir)
        self.population_file = population_file
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "variety_specific_variants").mkdir(exist_ok=True)
        (self.output_dir / "primer_sequences").mkdir(exist_ok=True)
        (self.output_dir / "validation").mkdir(exist_ok=True)
        (self.output_dir / "database").mkdir(exist_ok=True)
        (self.output_dir / "logs").mkdir(exist_ok=True)
        
        # Initialize data structures
        self.samples = []
        self.population_assignments = {}
        self.variety_specific_variants = {}
        self.primer_database = []
        
        logger.info(f"Initialized KiwiPrimerDesigner with output directory: {self.output_dir}")
    
    def load_population_data(self):
        """Load population assignments if available"""
        if self.population_file and os.path.exists(self.population_file):
            try:
                pop_df = pd.read_csv(self.population_file)
                for _, row in pop_df.iterrows():
                    sample = row['Sample'].replace('"', '')  # Remove quotes if present
                    pop_group = row['PopGroup']
                    self.population_assignments[sample] = pop_group
                logger.info(f"Loaded population assignments for {len(self.population_assignments)} samples")
            except Exception as e:
                logger.warning(f"Could not load population file: {e}")
    
    def analyze_vcf_structure(self):
        """Analyze VCF file structure and extract sample information"""
        logger.info("Analyzing VCF structure...")
        
        try:
            # Get sample names
            result = subprocess.run(
                ['bcftools', 'query', '-l', self.vcf_file],
                capture_output=True, text=True, check=True
            )
            self.samples = result.stdout.strip().split('\n')
            logger.info(f"Found {len(self.samples)} samples in VCF")
            
            # Get basic VCF statistics
            result = subprocess.run(
                ['bcftools', 'stats', self.vcf_file],
                capture_output=True, text=True, check=True
            )
            
            # Parse statistics
            stats_lines = result.stdout.split('\n')
            for line in stats_lines:
                if line.startswith('SN'):
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        if 'number of records' in parts[2]:
                            total_variants = int(parts[3])
                        elif 'number of SNPs' in parts[2]:
                            snp_count = int(parts[3])
                        elif 'number of indels' in parts[2]:
                            indel_count = int(parts[3])
            
            logger.info(f"VCF contains {total_variants} total variants ({snp_count} SNPs, {indel_count} INDELs)")
            
            return {
                'total_samples': len(self.samples),
                'total_variants': total_variants,
                'snp_count': snp_count,
                'indel_count': indel_count
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error analyzing VCF: {e}")
            return None
    
    def identify_variety_specific_variants(self, min_allele_freq=0.05, max_missing=0.1):
        """
        Identify variants that are specific or highly discriminative for each variety
        
        Args:
            min_allele_freq (float): Minimum allele frequency for variant consideration
            max_missing (float): Maximum missing data rate allowed
        """
        logger.info("Identifying variety-specific variants...")
        
        # Extract variant information with genotype data
        cmd = [
            'bcftools', 'query',
            '-f', '%CHROM\t%POS\t%REF\t%ALT[\t%GT]\n',
            self.vcf_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            variant_lines = result.stdout.strip().split('\n')
            
            logger.info(f"Processing {len(variant_lines)} variants...")
            
            variety_variants = defaultdict(list)
            
            for i, line in enumerate(variant_lines):
                if i % 1000 == 0:
                    logger.info(f"Processed {i} variants...")
                
                parts = line.split('\t')
                if len(parts) < 4 + len(self.samples):
                    continue
                
                chrom, pos, ref, alt = parts[:4]
                genotypes = parts[4:]
                
                # Calculate variant statistics
                variant_stats = self.calculate_variant_stats(genotypes)
                
                if variant_stats['missing_rate'] > max_missing:
                    continue
                
                # Find varieties with unique or highly specific genotype patterns
                specific_varieties = self.find_specific_varieties(genotypes, variant_stats)
                
                for variety_idx in specific_varieties:
                    variety_name = self.samples[variety_idx]
                    variant_info = {
                        'chrom': chrom,
                        'pos': int(pos),
                        'ref': ref,
                        'alt': alt,
                        'genotype': genotypes[variety_idx],
                        'specificity_score': specific_varieties[variety_idx],
                        'allele_freq': variant_stats['alt_freq'],
                        'missing_rate': variant_stats['missing_rate']
                    }
                    variety_variants[variety_name].append(variant_info)
            
            self.variety_specific_variants = variety_variants
            
            # Save variety-specific variants
            self.save_variety_variants()
            
            logger.info(f"Identified variety-specific variants for {len(variety_variants)} varieties")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error extracting variants: {e}")
    
    def calculate_variant_stats(self, genotypes):
        """Calculate statistics for a variant across all samples"""
        total_samples = len(genotypes)
        missing_count = sum(1 for gt in genotypes if gt in ['./.', '.', './.']])
        
        # Count alleles
        allele_counts = defaultdict(int)
        total_alleles = 0
        
        for gt in genotypes:
            if gt not in ['./.', '.', './.']:
                if '/' in gt:
                    alleles = gt.split('/')
                elif '|' in gt:
                    alleles = gt.split('|')
                else:
                    alleles = [gt]
                
                for allele in alleles:
                    if allele != '.':
                        allele_counts[allele] += 1
                        total_alleles += 1
        
        # Calculate alternative allele frequency
        alt_freq = allele_counts.get('1', 0) / max(total_alleles, 1)
        missing_rate = missing_count / total_samples
        
        return {
            'alt_freq': alt_freq,
            'missing_rate': missing_rate,
            'allele_counts': dict(allele_counts),
            'total_alleles': total_alleles
        }
    
    def find_specific_varieties(self, genotypes, variant_stats, min_specificity=0.8):
        """
        Find varieties with specific genotype patterns for a variant
        
        Returns dict of variety_index: specificity_score
        """
        specific_varieties = {}
        
        # Count genotype patterns
        genotype_counts = defaultdict(int)
        for gt in genotypes:
            genotype_counts[gt] += 1
        
        total_samples = len(genotypes)
        
        for i, gt in enumerate(genotypes):
            if gt in ['./.', '.', './.']:  # Skip missing genotypes
                continue
            
            # Calculate specificity score
            # Higher score for rarer genotypes that are more discriminative
            gt_frequency = genotype_counts[gt] / total_samples
            specificity_score = 1 - gt_frequency
            
            # Additional scoring based on population structure if available
            if self.population_assignments:
                sample_name = self.samples[i]
                if sample_name in self.population_assignments:
                    # Bonus for variants that distinguish between populations
                    pop_group = self.population_assignments[sample_name]
                    # This could be enhanced with more sophisticated population-aware scoring
            
            if specificity_score >= min_specificity:
                specific_varieties[i] = specificity_score
        
        return specific_varieties
    
    def save_variety_variants(self):
        """Save variety-specific variants to files"""
        logger.info("Saving variety-specific variants...")
        
        for variety, variants in self.variety_specific_variants.items():
            if not variants:
                continue
            
            # Sort by specificity score
            variants.sort(key=lambda x: x['specificity_score'], reverse=True)
            
            # Save top variants for each variety
            output_file = self.output_dir / "variety_specific_variants" / f"{variety}_specific_variants.txt"
            
            with open(output_file, 'w') as f:
                f.write("CHROM\tPOS\tREF\tALT\tGENOTYPE\tSPECIFICITY_SCORE\tALLELE_FREQ\tMISSING_RATE\n")
                for var in variants[:50]:  # Top 50 variants per variety
                    f.write(f"{var['chrom']}\t{var['pos']}\t{var['ref']}\t{var['alt']}\t"
                           f"{var['genotype']}\t{var['specificity_score']:.4f}\t"
                           f"{var['allele_freq']:.4f}\t{var['missing_rate']:.4f}\n")
        
        # Create summary
        summary_file = self.output_dir / "variety_specific_variants" / "summary.txt"
        with open(summary_file, 'w') as f:
            f.write("Variety-Specific Variants Summary\n")
            f.write("=" * 40 + "\n\n")
            for variety, variants in self.variety_specific_variants.items():
                f.write(f"{variety}: {len(variants)} specific variants\n")
            f.write(f"\nTotal varieties with specific variants: {len(self.variety_specific_variants)}\n")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Design variety-specific primers for kiwi varieties")
    parser.add_argument("--vcf", required=True, help="Path to merged VCF file")
    parser.add_argument("--reference", required=True, help="Path to reference genome FASTA")
    parser.add_argument("--output", required=True, help="Output directory")
    parser.add_argument("--population", help="Population assignment file (optional)")
    parser.add_argument("--min-allele-freq", type=float, default=0.05, help="Minimum allele frequency")
    parser.add_argument("--max-missing", type=float, default=0.1, help="Maximum missing data rate")
    
    args = parser.parse_args()
    
    # Initialize primer designer
    designer = KiwiPrimerDesigner(
        vcf_file=args.vcf,
        reference_genome=args.reference,
        output_dir=args.output,
        population_file=args.population
    )
    
    # Load population data if available
    designer.load_population_data()
    
    # Analyze VCF structure
    vcf_stats = designer.analyze_vcf_structure()
    if not vcf_stats:
        logger.error("Failed to analyze VCF file")
        sys.exit(1)
    
    # Identify variety-specific variants
    designer.identify_variety_specific_variants(
        min_allele_freq=args.min_allele_freq,
        max_missing=args.max_missing
    )
    
    logger.info("Variety-specific variant identification completed!")

if __name__ == "__main__":
    main()
