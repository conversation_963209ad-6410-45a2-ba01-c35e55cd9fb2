#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Script for Kiwi Primer Design Pipeline
===========================================

This script tests the primer design pipeline setup and validates
that all dependencies are properly installed.

Author: Augment Agent
Date: 2025-07-12
"""

import subprocess
import sys
import os
from pathlib import Path
import pandas as pd

def check_command(command, description):
    """Check if a command is available"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ {description}: Available")
            return True
        else:
            print(f"✗ {description}: Not available or version check failed")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        try:
            # Try alternative version check
            result = subprocess.run([command, '-h'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ {description}: Available")
                return True
            else:
                print(f"✗ {description}: Not available")
                return False
        except:
            print(f"✗ {description}: Not available")
            return False

def check_python_packages():
    """Check required Python packages"""
    required_packages = [
        'pandas', 'numpy', 'biopython', 'pyyaml', 'matplotlib', 'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ Python package {package}: Available")
        except ImportError:
            print(f"✗ Python package {package}: Missing")
            missing_packages.append(package)
    
    return missing_packages

def check_input_files():
    """Check if required input files exist"""
    required_files = [
        'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz',
        'kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv'
    ]
    
    optional_files = [
        'kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna'
    ]
    
    all_good = True
    
    print("\nChecking required input files:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}: Found")
        else:
            print(f"✗ {file_path}: Missing")
            all_good = False
    
    print("\nChecking optional input files:")
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}: Found")
        else:
            print(f"? {file_path}: Not found (may need to be created)")
    
    return all_good

def test_vcf_access():
    """Test VCF file access and basic operations"""
    vcf_file = 'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz'
    
    if not os.path.exists(vcf_file):
        print(f"✗ Cannot test VCF access: {vcf_file} not found")
        return False
    
    try:
        # Test bcftools query
        result = subprocess.run(
            ['bcftools', 'query', '-l', vcf_file],
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            samples = result.stdout.strip().split('\n')
            print(f"✓ VCF access test: Found {len(samples)} samples")
            return True
        else:
            print(f"✗ VCF access test failed: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"✗ VCF access test failed: {e}")
        return False

def create_test_config():
    """Create a test configuration file"""
    config_content = """# Test Configuration for Kiwi Primer Design Pipeline
vcf_file: 'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz'
reference_genome: 'kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna'
population_file: 'kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv'
output_directory: 'test_primer_output'
min_allele_freq: 0.05
max_missing: 0.1
max_primers_per_variety: 5  # Reduced for testing
"""
    
    with open('test_primer_config.yaml', 'w') as f:
        f.write(config_content)
    
    print("✓ Created test configuration file: test_primer_config.yaml")

def run_basic_tests():
    """Run basic functionality tests"""
    print("=" * 60)
    print("KIWI PRIMER DESIGN PIPELINE - SYSTEM CHECK")
    print("=" * 60)
    
    # Check system commands
    print("\n1. Checking system dependencies:")
    commands_ok = True
    commands_ok &= check_command('bcftools', 'BCFtools')
    commands_ok &= check_command('samtools', 'SAMtools')
    commands_ok &= check_command('primer3_core', 'Primer3')
    commands_ok &= check_command('makeblastdb', 'BLAST makeblastdb')
    commands_ok &= check_command('blastn', 'BLAST blastn')
    
    # Check Python packages
    print("\n2. Checking Python packages:")
    missing_packages = check_python_packages()
    
    # Check input files
    print("\n3. Checking input files:")
    files_ok = check_input_files()
    
    # Test VCF access
    print("\n4. Testing VCF file access:")
    vcf_ok = test_vcf_access()
    
    # Create test config
    print("\n5. Creating test configuration:")
    create_test_config()
    
    # Summary
    print("\n" + "=" * 60)
    print("SYSTEM CHECK SUMMARY")
    print("=" * 60)
    
    if commands_ok:
        print("✓ All system commands available")
    else:
        print("✗ Some system commands missing - install required software")
    
    if not missing_packages:
        print("✓ All Python packages available")
    else:
        print(f"✗ Missing Python packages: {', '.join(missing_packages)}")
        print("  Install with: pip install " + " ".join(missing_packages))
    
    if files_ok:
        print("✓ Required input files found")
    else:
        print("✗ Some required input files missing")
    
    if vcf_ok:
        print("✓ VCF file access successful")
    else:
        print("✗ VCF file access failed")
    
    # Overall assessment
    all_ok = commands_ok and not missing_packages and files_ok and vcf_ok
    
    print("\n" + "-" * 60)
    if all_ok:
        print("🎉 SYSTEM CHECK PASSED - Ready to run primer design pipeline!")
        print("\nNext steps:")
        print("1. Review and modify test_primer_config.yaml if needed")
        print("2. Run: python run_variety_primer_design.py --config test_primer_config.yaml")
    else:
        print("⚠️  SYSTEM CHECK FAILED - Please address the issues above")
        print("\nCommon solutions:")
        print("- Install missing software packages")
        print("- Install missing Python packages with pip")
        print("- Check file paths in your data directory")
    
    return all_ok

def main():
    """Main test function"""
    try:
        success = run_basic_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
