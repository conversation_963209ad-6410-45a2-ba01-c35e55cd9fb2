#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Primer Validation and Quality Assessment Tools
==============================================

Additional tools for validating and assessing the quality of designed primers
for kiwi variety identification.

Author: Augment Agent
Date: 2025-07-12
"""

import pandas as pd
import numpy as np
import subprocess
import os
import tempfile
import logging
from pathlib import Path
from Bio.Seq import Seq
from Bio import SeqIO
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

class PrimerValidator:
    """Advanced primer validation and quality assessment"""
    
    def __init__(self, primer_database_file, reference_genome):
        """
        Initialize primer validator
        
        Args:
            primer_database_file (str): Path to primer database CSV
            reference_genome (str): Path to reference genome
        """
        self.primer_db = pd.read_csv(primer_database_file)
        self.reference_genome = reference_genome
        self.validation_results = {}
        
        logger.info(f"Loaded {len(self.primer_db)} primer pairs for validation")
    
    def validate_primer_multiplexing(self, primer_subset=None, max_combinations=100):
        """
        Assess potential for primer multiplexing by checking cross-reactivity
        
        Args:
            primer_subset (list): Subset of primer IDs to test (None for all)
            max_combinations (int): Maximum number of combinations to test
            
        Returns:
            dict: Multiplexing compatibility results
        """
        logger.info("Assessing primer multiplexing potential...")
        
        if primer_subset is None:
            primers_to_test = self.primer_db.copy()
        else:
            primers_to_test = self.primer_db[self.primer_db['Primer_ID'].isin(primer_subset)]
        
        # Test combinations of primers
        multiplexing_results = {}
        tested_combinations = 0
        
        for i, primer1 in primers_to_test.iterrows():
            if tested_combinations >= max_combinations:
                break
                
            for j, primer2 in primers_to_test.iterrows():
                if i >= j or tested_combinations >= max_combinations:
                    continue
                
                # Check cross-reactivity between primer pairs
                compatibility = self.check_primer_compatibility(primer1, primer2)
                
                combo_id = f"{primer1['Primer_ID']}_{primer2['Primer_ID']}"
                multiplexing_results[combo_id] = compatibility
                tested_combinations += 1
        
        logger.info(f"Tested {tested_combinations} primer combinations")
        return multiplexing_results
    
    def check_primer_compatibility(self, primer1, primer2):
        """
        Check compatibility between two primer pairs for multiplexing
        
        Args:
            primer1 (pd.Series): First primer pair data
            primer2 (pd.Series): Second primer pair data
            
        Returns:
            dict: Compatibility assessment
        """
        # Check Tm compatibility (should be within 5°C)
        tm_diff_left = abs(primer1['Left_Tm'] - primer2['Left_Tm'])
        tm_diff_right = abs(primer1['Right_Tm'] - primer2['Right_Tm'])
        tm_compatible = tm_diff_left <= 5.0 and tm_diff_right <= 5.0
        
        # Check product size compatibility (should be distinguishable)
        size_diff = abs(primer1['Product_Size'] - primer2['Product_Size'])
        size_compatible = size_diff >= 50  # At least 50bp difference
        
        # Check for cross-primer interactions
        cross_interactions = self.check_cross_primer_interactions(primer1, primer2)
        
        compatibility_score = 0
        if tm_compatible:
            compatibility_score += 0.4
        if size_compatible:
            compatibility_score += 0.4
        if not cross_interactions['high_risk']:
            compatibility_score += 0.2
        
        return {
            'tm_compatible': tm_compatible,
            'size_compatible': size_compatible,
            'cross_interactions': cross_interactions,
            'compatibility_score': compatibility_score,
            'recommended_for_multiplex': compatibility_score >= 0.8
        }
    
    def check_cross_primer_interactions(self, primer1, primer2):
        """Check for potential cross-interactions between primer pairs"""
        primers = [
            primer1['Left_Primer'], primer1['Right_Primer'],
            primer2['Left_Primer'], primer2['Right_Primer']
        ]
        
        high_risk_interactions = 0
        medium_risk_interactions = 0
        
        # Check all pairwise interactions
        for i in range(len(primers)):
            for j in range(i + 1, len(primers)):
                interaction_risk = self.assess_primer_interaction(primers[i], primers[j])
                
                if interaction_risk == 'HIGH':
                    high_risk_interactions += 1
                elif interaction_risk == 'MEDIUM':
                    medium_risk_interactions += 1
        
        return {
            'high_risk': high_risk_interactions > 0,
            'medium_risk': medium_risk_interactions > 0,
            'high_risk_count': high_risk_interactions,
            'medium_risk_count': medium_risk_interactions
        }
    
    def assess_primer_interaction(self, primer1, primer2):
        """Assess interaction risk between two individual primers"""
        # Simple complementarity check
        max_complementarity = 0
        max_3prime_complementarity = 0
        
        # Check complementarity in all orientations
        for p1 in [primer1, str(Seq(primer1).reverse_complement())]:
            for p2 in [primer2, str(Seq(primer2).reverse_complement())]:
                comp_score = self.calculate_complementarity(p1, p2)
                max_complementarity = max(max_complementarity, comp_score['total'])
                max_3prime_complementarity = max(max_3prime_complementarity, comp_score['3prime'])
        
        # Risk assessment
        if max_3prime_complementarity >= 4:
            return 'HIGH'
        elif max_3prime_complementarity >= 3 or max_complementarity >= 8:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def calculate_complementarity(self, seq1, seq2):
        """Calculate complementarity between two sequences"""
        complements = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G'}
        
        max_total = 0
        max_3prime = 0
        
        # Check all possible alignments
        for i in range(len(seq1)):
            for j in range(len(seq2)):
                comp_count = 0
                for k in range(min(len(seq1) - i, len(seq2) - j)):
                    if complements.get(seq1[i + k].upper()) == seq2[j + k].upper():
                        comp_count += 1
                    else:
                        break
                
                max_total = max(max_total, comp_count)
                
                # Check 3' complementarity (last 5 bases)
                if i >= len(seq1) - 5:
                    max_3prime = max(max_3prime, comp_count)
        
        return {'total': max_total, '3prime': max_3prime}
    
    def generate_primer_selection_recommendations(self, output_file):
        """
        Generate recommendations for primer selection based on quality metrics
        
        Args:
            output_file (str): Path to output recommendations file
        """
        logger.info("Generating primer selection recommendations...")
        
        recommendations = {}
        
        # Group primers by variety
        for variety in self.primer_db['Variety'].unique():
            variety_primers = self.primer_db[self.primer_db['Variety'] == variety].copy()
            
            # Sort by overall quality
            variety_primers = variety_primers.sort_values('Overall_Quality', ascending=False)
            
            # Select top primers
            top_primers = variety_primers.head(3)  # Top 3 primers per variety
            
            recommendations[variety] = {
                'total_primers': len(variety_primers),
                'recommended_primers': top_primers[['Primer_ID', 'Overall_Quality', 'Product_Size']].to_dict('records'),
                'avg_quality': variety_primers['Overall_Quality'].mean(),
                'quality_range': [variety_primers['Overall_Quality'].min(), variety_primers['Overall_Quality'].max()]
            }
        
        # Save recommendations
        with open(output_file, 'w') as f:
            json.dump(recommendations, f, indent=2, default=str)
        
        # Generate summary report
        summary_file = output_file.replace('.json', '_summary.txt')
        with open(summary_file, 'w') as f:
            f.write("Kiwi Variety Primer Selection Recommendations\n")
            f.write("=" * 50 + "\n\n")
            
            # Overall statistics
            total_varieties = len(recommendations)
            avg_quality_all = self.primer_db['Overall_Quality'].mean()
            
            f.write(f"Total varieties: {total_varieties}\n")
            f.write(f"Average primer quality: {avg_quality_all:.3f}\n\n")
            
            # Top varieties by quality
            variety_qualities = [(v, data['avg_quality']) for v, data in recommendations.items()]
            variety_qualities.sort(key=lambda x: x[1], reverse=True)
            
            f.write("TOP 10 VARIETIES BY PRIMER QUALITY:\n")
            f.write("-" * 35 + "\n")
            for variety, quality in variety_qualities[:10]:
                f.write(f"{variety}: {quality:.3f}\n")
            
            f.write("\nRECOMMENDATIONS FOR PRIMER SELECTION:\n")
            f.write("-" * 40 + "\n")
            f.write("1. Use primers with Overall_Quality >= 0.7 for reliable results\n")
            f.write("2. For multiplexing, select primers with compatible Tm values (±5°C)\n")
            f.write("3. Ensure product sizes are distinguishable (≥50bp difference)\n")
            f.write("4. Validate primers experimentally before large-scale use\n")
        
        logger.info(f"Recommendations saved to {output_file}")
    
    def create_primer_visualization(self, output_dir):
        """Create visualizations of primer quality and characteristics"""
        logger.info("Creating primer quality visualizations...")
        
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Quality distribution
        plt.figure(figsize=(10, 6))
        plt.hist(self.primer_db['Overall_Quality'], bins=20, alpha=0.7, edgecolor='black')
        plt.xlabel('Overall Quality Score')
        plt.ylabel('Number of Primer Pairs')
        plt.title('Distribution of Primer Quality Scores')
        plt.axvline(x=0.7, color='red', linestyle='--', label='Recommended Threshold (0.7)')
        plt.legend()
        plt.tight_layout()
        plt.savefig(output_dir / 'primer_quality_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Tm distribution
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        ax1.hist(self.primer_db['Left_Tm'], bins=20, alpha=0.7, label='Left Primers')
        ax1.hist(self.primer_db['Right_Tm'], bins=20, alpha=0.7, label='Right Primers')
        ax1.set_xlabel('Melting Temperature (°C)')
        ax1.set_ylabel('Number of Primers')
        ax1.set_title('Primer Melting Temperature Distribution')
        ax1.legend()
        
        ax2.scatter(self.primer_db['Left_Tm'], self.primer_db['Right_Tm'], alpha=0.6)
        ax2.plot([50, 70], [50, 70], 'r--', label='Perfect Match')
        ax2.set_xlabel('Left Primer Tm (°C)')
        ax2.set_ylabel('Right Primer Tm (°C)')
        ax2.set_title('Primer Pair Tm Correlation')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig(output_dir / 'primer_tm_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Product size distribution
        plt.figure(figsize=(10, 6))
        plt.hist(self.primer_db['Product_Size'], bins=30, alpha=0.7, edgecolor='black')
        plt.xlabel('PCR Product Size (bp)')
        plt.ylabel('Number of Primer Pairs')
        plt.title('Distribution of PCR Product Sizes')
        plt.axvline(x=self.primer_db['Product_Size'].mean(), color='red', 
                   linestyle='--', label=f'Mean: {self.primer_db["Product_Size"].mean():.0f} bp')
        plt.legend()
        plt.tight_layout()
        plt.savefig(output_dir / 'product_size_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. Quality by variety (top 20 varieties)
        variety_quality = self.primer_db.groupby('Variety')['Overall_Quality'].agg(['mean', 'count']).reset_index()
        variety_quality = variety_quality[variety_quality['count'] >= 3]  # At least 3 primers
        variety_quality = variety_quality.sort_values('mean', ascending=False).head(20)
        
        plt.figure(figsize=(12, 8))
        bars = plt.bar(range(len(variety_quality)), variety_quality['mean'])
        plt.xlabel('Variety')
        plt.ylabel('Average Quality Score')
        plt.title('Average Primer Quality by Variety (Top 20)')
        plt.xticks(range(len(variety_quality)), variety_quality['Variety'], rotation=45, ha='right')
        plt.axhline(y=0.7, color='red', linestyle='--', label='Recommended Threshold')
        plt.legend()
        plt.tight_layout()
        plt.savefig(output_dir / 'quality_by_variety.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Visualizations saved to {output_dir}")

def main():
    """Main function for running primer validation"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate and assess primer quality")
    parser.add_argument("--database", required=True, help="Primer database CSV file")
    parser.add_argument("--reference", required=True, help="Reference genome FASTA")
    parser.add_argument("--output", required=True, help="Output directory")
    
    args = parser.parse_args()
    
    # Initialize validator
    validator = PrimerValidator(args.database, args.reference)
    
    # Generate recommendations
    recommendations_file = Path(args.output) / "primer_recommendations.json"
    validator.generate_primer_selection_recommendations(str(recommendations_file))
    
    # Create visualizations
    validator.create_primer_visualization(args.output)
    
    print(f"Validation results saved to {args.output}")

if __name__ == "__main__":
    main()
