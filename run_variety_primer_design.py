#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Complete Workflow for Kiwi Variety-Specific Primer Design
=========================================================

This script runs the complete pipeline from VCF analysis to primer design
and validation for 110 kiwi varieties.

Usage:
    python run_variety_primer_design.py --config config.yaml

Author: Augment Agent
Date: 2025-07-12
"""

import argparse
import yaml
import logging
import sys
import os
import pandas as pd
from pathlib import Path
import json
from datetime import datetime

# Import our custom modules
from kiwi_variety_specific_primer_design import KiwiPrimerDesigner
from primer_design_module import PrimerDesigner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KiwiPrimerWorkflow:
    """Complete workflow for kiwi variety-specific primer design"""
    
    def __init__(self, config_file):
        """Initialize workflow with configuration"""
        self.config = self.load_config(config_file)
        self.output_dir = Path(self.config['output_directory'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging to file
        log_file = self.output_dir / "primer_design_workflow.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        logger.info("Initialized Kiwi Primer Design Workflow")
        logger.info(f"Output directory: {self.output_dir}")
    
    def load_config(self, config_file):
        """Load configuration from YAML file"""
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Failed to load config file {config_file}: {e}")
            sys.exit(1)
    
    def validate_inputs(self):
        """Validate that all required input files exist"""
        logger.info("Validating input files...")
        
        required_files = [
            self.config['vcf_file'],
            self.config['reference_genome']
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.error(f"Required file not found: {file_path}")
                return False
        
        # Check optional files
        if 'population_file' in self.config and self.config['population_file']:
            if not os.path.exists(self.config['population_file']):
                logger.warning(f"Population file not found: {self.config['population_file']}")
        
        logger.info("Input validation completed successfully")
        return True
    
    def run_variant_analysis(self):
        """Run variety-specific variant identification"""
        logger.info("Starting variety-specific variant analysis...")
        
        # Initialize variant analyzer
        analyzer = KiwiPrimerDesigner(
            vcf_file=self.config['vcf_file'],
            reference_genome=self.config['reference_genome'],
            output_dir=str(self.output_dir),
            population_file=self.config.get('population_file')
        )
        
        # Load population data
        analyzer.load_population_data()
        
        # Analyze VCF structure
        vcf_stats = analyzer.analyze_vcf_structure()
        if not vcf_stats:
            logger.error("Failed to analyze VCF file")
            return False
        
        # Save VCF statistics
        stats_file = self.output_dir / "vcf_analysis_stats.json"
        with open(stats_file, 'w') as f:
            json.dump(vcf_stats, f, indent=2)
        
        # Identify variety-specific variants
        analyzer.identify_variety_specific_variants(
            min_allele_freq=self.config.get('min_allele_freq', 0.05),
            max_missing=self.config.get('max_missing', 0.1)
        )
        
        self.analyzer = analyzer
        logger.info("Variety-specific variant analysis completed")
        return True
    
    def run_primer_design(self):
        """Run primer design for all varieties"""
        logger.info("Starting primer design for all varieties...")
        
        # Initialize primer designer
        primer_designer = PrimerDesigner(
            reference_genome=self.config['reference_genome'],
            output_dir=str(self.output_dir)
        )
        
        # Get list of varieties with specific variants
        variants_dir = self.output_dir / "variety_specific_variants"
        variant_files = list(variants_dir.glob("*_specific_variants.txt"))
        
        if not variant_files:
            logger.error("No variety-specific variant files found")
            return False
        
        logger.info(f"Found {len(variant_files)} varieties with specific variants")
        
        # Design primers for each variety
        all_primers = []
        successful_varieties = 0
        
        for variant_file in variant_files:
            variety_name = variant_file.stem.replace('_specific_variants', '')
            
            try:
                variety_primers = primer_designer.design_primers_for_variety(
                    variety_name=variety_name,
                    variants_file=str(variant_file),
                    max_primers_per_variety=self.config.get('max_primers_per_variety', 10)
                )
                
                if variety_primers:
                    all_primers.extend(variety_primers)
                    successful_varieties += 1
                    
                    # Save variety-specific results
                    self.save_variety_primer_results(variety_name, variety_primers)
                
            except Exception as e:
                logger.error(f"Failed to design primers for {variety_name}: {e}")
        
        logger.info(f"Successfully designed primers for {successful_varieties} varieties")
        logger.info(f"Total primer pairs designed: {len(all_primers)}")
        
        # Save comprehensive results
        self.save_comprehensive_results(all_primers)
        
        return len(all_primers) > 0
    
    def save_variety_primer_results(self, variety_name, primers):
        """Save primer results for a specific variety"""
        variety_dir = self.output_dir / "variety_results" / variety_name
        variety_dir.mkdir(parents=True, exist_ok=True)
        
        # Convert primers to DataFrame
        primer_records = []
        for i, primer in enumerate(primers):
            record = {
                'Variety': variety_name,
                'Primer_ID': f"{variety_name}_P{i+1:02d}",
                'Variant_ID': f"{primer['variant_info']['chrom']}:{primer['variant_info']['pos']}",
                'Left_Primer': primer['left_primer'],
                'Right_Primer': primer['right_primer'],
                'Left_Tm': primer['left_tm'],
                'Right_Tm': primer['right_tm'],
                'Left_GC': primer['left_gc'],
                'Right_GC': primer['right_gc'],
                'Product_Size': primer['product_size'],
                'Left_Specificity': primer['left_validation']['specificity_score'],
                'Right_Specificity': primer['right_validation']['specificity_score'],
                'Dimer_Risk': primer['dimer_check']['dimer_risk'],
                'Overall_Quality': primer['overall_quality'],
                'Variant_Specificity': primer['variant_info']['specificity_score']
            }
            primer_records.append(record)
        
        # Save to CSV
        df = pd.DataFrame(primer_records)
        df.to_csv(variety_dir / f"{variety_name}_primers.csv", index=False)
        
        # Save detailed JSON
        with open(variety_dir / f"{variety_name}_primers_detailed.json", 'w') as f:
            json.dump(primers, f, indent=2, default=str)
    
    def save_comprehensive_results(self, all_primers):
        """Save comprehensive results across all varieties"""
        logger.info("Saving comprehensive primer database...")
        
        # Create master primer database
        master_records = []
        variety_summary = {}
        
        for primer in all_primers:
            variety = primer['variety']
            
            # Update variety summary
            if variety not in variety_summary:
                variety_summary[variety] = {
                    'total_primers': 0,
                    'high_quality_primers': 0,
                    'avg_quality': 0
                }
            
            variety_summary[variety]['total_primers'] += 1
            if primer['overall_quality'] >= 0.7:
                variety_summary[variety]['high_quality_primers'] += 1
            
            # Create master record
            record = {
                'Variety': variety,
                'Primer_ID': f"{variety}_P{variety_summary[variety]['total_primers']:02d}",
                'Chromosome': primer['variant_info']['chrom'],
                'Position': primer['variant_info']['pos'],
                'Variant_ID': f"{primer['variant_info']['chrom']}:{primer['variant_info']['pos']}",
                'Left_Primer': primer['left_primer'],
                'Right_Primer': primer['right_primer'],
                'Left_Tm': primer['left_tm'],
                'Right_Tm': primer['right_tm'],
                'Product_Size': primer['product_size'],
                'Overall_Quality': primer['overall_quality'],
                'Recommended': 'Yes' if primer['overall_quality'] >= 0.7 else 'No'
            }
            master_records.append(record)
        
        # Calculate average quality for each variety
        for variety in variety_summary:
            variety_primers = [p for p in all_primers if p['variety'] == variety]
            if variety_primers:
                avg_quality = sum(p['overall_quality'] for p in variety_primers) / len(variety_primers)
                variety_summary[variety]['avg_quality'] = avg_quality
        
        # Save master database
        master_df = pd.DataFrame(master_records)
        master_df.to_csv(self.output_dir / "kiwi_variety_primers_database.csv", index=False)
        
        # Save variety summary
        summary_df = pd.DataFrame.from_dict(variety_summary, orient='index')
        summary_df.index.name = 'Variety'
        summary_df.to_csv(self.output_dir / "variety_primer_summary.csv")
        
        # Generate final report
        self.generate_final_report(master_df, summary_df)
    
    def generate_final_report(self, master_df, summary_df):
        """Generate final analysis report"""
        report_file = self.output_dir / "primer_design_report.txt"
        
        with open(report_file, 'w') as f:
            f.write("Kiwi Variety-Specific Primer Design Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Overall statistics
            f.write("OVERALL STATISTICS\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total varieties analyzed: {len(summary_df)}\n")
            f.write(f"Total primer pairs designed: {len(master_df)}\n")
            f.write(f"High-quality primers (≥0.7): {len(master_df[master_df['Recommended'] == 'Yes'])}\n")
            f.write(f"Average primers per variety: {len(master_df) / len(summary_df):.1f}\n\n")
            
            # Quality distribution
            f.write("QUALITY DISTRIBUTION\n")
            f.write("-" * 20 + "\n")
            quality_bins = [0.9, 0.8, 0.7, 0.6, 0.5, 0.0]
            for i in range(len(quality_bins) - 1):
                count = len(master_df[(master_df['Overall_Quality'] >= quality_bins[i+1]) & 
                                    (master_df['Overall_Quality'] < quality_bins[i])])
                f.write(f"Quality {quality_bins[i+1]:.1f}-{quality_bins[i]:.1f}: {count} primers\n")
            
            f.write("\nTOP 10 VARIETIES BY PRIMER QUALITY\n")
            f.write("-" * 35 + "\n")
            top_varieties = summary_df.sort_values('avg_quality', ascending=False).head(10)
            for variety, row in top_varieties.iterrows():
                f.write(f"{variety}: {row['avg_quality']:.3f} avg quality, "
                       f"{row['high_quality_primers']}/{row['total_primers']} high-quality\n")
            
            f.write(f"\nDetailed results saved to: {self.output_dir}\n")
            f.write("- Master database: kiwi_variety_primers_database.csv\n")
            f.write("- Variety summary: variety_primer_summary.csv\n")
            f.write("- Individual variety results: variety_results/\n")
    
    def run_complete_workflow(self):
        """Run the complete primer design workflow"""
        logger.info("Starting complete kiwi variety-specific primer design workflow")
        
        # Validate inputs
        if not self.validate_inputs():
            logger.error("Input validation failed")
            return False
        
        # Run variant analysis
        if not self.run_variant_analysis():
            logger.error("Variant analysis failed")
            return False
        
        # Run primer design
        if not self.run_primer_design():
            logger.error("Primer design failed")
            return False
        
        logger.info("Complete workflow finished successfully!")
        logger.info(f"Results available in: {self.output_dir}")
        return True

def create_default_config():
    """Create a default configuration file"""
    config = {
        'vcf_file': 'kiwi_pangenome_population/04.variant_calling/merged_variants/kiwi_all_samples.vcf.gz',
        'reference_genome': 'kiwi_pangenome_population/01.genome_assessment/processed_genomes/GCA_003024255.1.processed.fna',
        'population_file': 'kiwi_pangenome_population/05.population_analysis/population_assignments_K4.csv',
        'output_directory': 'kiwi_variety_primers_output',
        'min_allele_freq': 0.05,
        'max_missing': 0.1,
        'max_primers_per_variety': 10
    }
    
    with open('primer_design_config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print("Created default configuration file: primer_design_config.yaml")
    print("Please review and modify the file paths as needed before running the workflow.")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Kiwi variety-specific primer design workflow")
    parser.add_argument("--config", help="Configuration file (YAML format)")
    parser.add_argument("--create-config", action="store_true", 
                       help="Create a default configuration file")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_default_config()
        return
    
    if not args.config:
        logger.error("Please provide a configuration file with --config or create one with --create-config")
        sys.exit(1)
    
    # Run workflow
    workflow = KiwiPrimerWorkflow(args.config)
    success = workflow.run_complete_workflow()
    
    if success:
        logger.info("Workflow completed successfully!")
        sys.exit(0)
    else:
        logger.error("Workflow failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
